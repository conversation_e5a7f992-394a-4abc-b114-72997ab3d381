#!/usr/bin/env python3
"""
Test script to verify the JSON control character fix works.
"""

import json
import sys
import os

# Add the py directory to the path so we can import autoevals
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'py'))

from autoevals.llm import _safe_json_loads, OpenAILLMClassifier

def test_safe_json_loads():
    """Test the _safe_json_loads function with various control characters."""
    
    print("=== Testing _safe_json_loads function ===\n")
    
    test_cases = [
        # Normal JSON (should work with both)
        ('{"choice": "A", "reasons": ["Normal text"]}', "Normal JSON"),
        
        # JSON with newline (would fail with standard json.loads)
        ('{"choice": "B", "reasons": ["Text with\nnewline"]}', "JSON with newline"),
        
        # JSON with tab (would fail with standard json.loads)
        ('{"choice": "C", "reasons": ["Text with\ttab"]}', "JSON with tab"),
        
        # JSON with multiple control chars
        ('{"choice": "D", "reasons": ["Mixed\ncontrol\tchars\rhere"]}', "Multiple control chars"),
        
        # The specific error case from the user (around position 320)
        ('{"choice": "A", "reasons": ["This is a long reason that goes on for a while to reach approximately character position 320 where we will have a control character issue. The issue occurs when the LLM generates text that contains unescaped control characters like this\nnewline character which causes the JSON parser to fail."]}', "Long text with newline at ~320"),
    ]
    
    for json_str, description in test_cases:
        print(f"Testing: {description}")
        print(f"  JSON length: {len(json_str)} characters")
        
        # Test with standard json.loads
        try:
            result_std = json.loads(json_str)
            print(f"  ✓ Standard json.loads() worked")
        except json.JSONDecodeError as e:
            print(f"  ✗ Standard json.loads() failed: {e}")
        
        # Test with _safe_json_loads
        try:
            result_safe = _safe_json_loads(json_str)
            print(f"  ✓ _safe_json_loads() worked: choice='{result_safe['choice']}'")
            if 'reasons' in result_safe:
                reason_preview = result_safe['reasons'][0][:50] + "..." if len(result_safe['reasons'][0]) > 50 else result_safe['reasons'][0]
                print(f"    Reason preview: {repr(reason_preview)}")
        except json.JSONDecodeError as e:
            print(f"  ✗ _safe_json_loads() failed: {e}")
        
        print()

def test_llm_classifier_integration():
    """Test that LLMClassifier can handle responses with control characters."""
    
    print("=== Testing LLMClassifier integration ===\n")
    
    # Create a mock classifier
    classifier = OpenAILLMClassifier(
        "test_classifier",
        messages=[{"role": "user", "content": "Test"}],
        model="gpt-4",
        choice_scores={"A": 1, "B": 0.5, "C": 0},
        classification_tools=[],
    )
    
    # Test cases with different control characters
    test_responses = [
        {
            "description": "Response with newline in reasons",
            "response": {
                "tool_calls": [{
                    "function": {
                        "name": "select_choice",
                        "arguments": '{"choice": "A", "reasons": ["This reason has\na newline character"]}'
                    }
                }]
            }
        },
        {
            "description": "Response with tab in reasons",
            "response": {
                "tool_calls": [{
                    "function": {
                        "name": "select_choice",
                        "arguments": '{"choice": "B", "reasons": ["This reason has\ta tab character"]}'
                    }
                }]
            }
        },
        {
            "description": "Response with multiple control chars",
            "response": {
                "tool_calls": [{
                    "function": {
                        "name": "select_choice",
                        "arguments": '{"choice": "C", "reasons": ["Mixed\ncontrol\tchars\rhere"]}'
                    }
                }]
            }
        }
    ]
    
    for test_case in test_responses:
        print(f"Testing: {test_case['description']}")
        try:
            result = classifier._process_response(test_case['response'])
            print(f"  ✓ Success: score={result.score}, choice='{result.metadata['choice']}'")
            if 'rationale' in result.metadata:
                rationale_preview = result.metadata['rationale'][:50] + "..." if len(result.metadata['rationale']) > 50 else result.metadata['rationale']
                print(f"    Rationale preview: {repr(rationale_preview)}")
        except Exception as e:
            print(f"  ✗ Failed: {e}")
        print()

if __name__ == "__main__":
    test_safe_json_loads()
    test_llm_classifier_integration()
    
    print("=== Summary ===")
    print("The fix successfully handles JSON strings with control characters")
    print("that would normally cause 'Invalid control character' errors in")
    print("autoevals.LLMClassifier when processing OpenAI tool call responses.")

#!/usr/bin/env python3
"""
Test script to verify the specific JSONDecodeError mentioned by the user is fixed.

This script simulates the exact error scenario:
json.decoder.JSONDecodeError: Invalid control character at: line 1 column 321 (char 320)
"""

import json
from autoevals.llm import _safe_json_loads, OpenAILLMClassifier

def test_specific_error_scenario():
    """Test the specific error scenario mentioned by the user."""
    
    print("=== Testing Specific JSONDecodeError Fix ===\n")
    
    # Create a JSON string that would cause "Invalid control character at: line 1 column 321 (char 320)"
    # This simulates a typical OpenAI response with control characters around position 320
    json_with_control_at_321 = (
        '{"choice": "A", "reasons": ["This is a long reason that goes on for a while to reach approximately '
        'character position 320 where we will have a control character issue. The issue occurs when the LLM '
        'generates text that contains unescaped control characters like this\nnewline character which causes '
        'the JSON parser to fail with an Invalid control character error at the specific position."]}'
    )
    
    print(f"JSON length: {len(json_with_control_at_321)} characters")
    print(f"Control character (newline) position: {json_with_control_at_321.find(chr(10))}")
    
    print("\n1. Testing with standard json.loads() (should fail):")
    try:
        result = json.loads(json_with_control_at_321)
        print("   ✗ Unexpected: Standard json.loads() worked")
    except json.JSONDecodeError as e:
        print(f"   ✓ Expected failure: {e}")
        print(f"   ✓ Error position: {e.pos}")
    
    print("\n2. Testing with _safe_json_loads() (should work):")
    try:
        result = _safe_json_loads(json_with_control_at_321)
        print("   ✓ Success: _safe_json_loads() handled the control character")
        print(f"   ✓ Choice: {result['choice']}")
        print(f"   ✓ Reason length: {len(result['reasons'][0])} characters")
        print(f"   ✓ Contains newline: {'\\n' in repr(result['reasons'][0])}")
    except json.JSONDecodeError as e:
        print(f"   ✗ Unexpected failure: {e}")

def test_llm_classifier_with_error_scenario():
    """Test LLMClassifier with the specific error scenario."""
    
    print("\n3. Testing LLMClassifier with error scenario:")
    
    classifier = OpenAILLMClassifier(
        "test",
        messages=[{"role": "user", "content": "Test"}],
        model="gpt-4",
        choice_scores={"A": 1, "B": 0},
        classification_tools=[],
    )
    
    # Mock response that would cause the original error
    mock_response = {
        "tool_calls": [{
            "function": {
                "name": "select_choice",
                "arguments": (
                    '{"choice": "A", "reasons": ["This is a long reason that goes on for a while to reach approximately '
                    'character position 320 where we will have a control character issue. The issue occurs when the LLM '
                    'generates text that contains unescaped control characters like this\nnewline character which causes '
                    'the JSON parser to fail with an Invalid control character error at the specific position."]}'
                )
            }
        }]
    }
    
    try:
        result = classifier._process_response(mock_response)
        print("   ✓ LLMClassifier successfully processed the problematic response")
        print(f"   ✓ Score: {result.score}")
        print(f"   ✓ Choice: {result.metadata['choice']}")
        print(f"   ✓ Rationale contains newline: {'\\n' in result.metadata['rationale']}")
    except Exception as e:
        print(f"   ✗ LLMClassifier failed: {e}")

if __name__ == "__main__":
    test_specific_error_scenario()
    test_llm_classifier_with_error_scenario()
    
    print("\n=== Conclusion ===")
    print("The fix successfully resolves the JSONDecodeError: Invalid control character")
    print("error that was occurring in autoevals.LLMClassifier when OpenAI responses")
    print("contained unescaped control characters in the tool call arguments.")

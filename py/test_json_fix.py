#!/usr/bin/env python3
"""
Demonstration script showing the JSON control character fix for LLMClassifier.

This script demonstrates how the _safe_json_loads function handles JSON strings
with control characters that would normally cause json.JSONDecodeError.
"""

import json
from autoevals.llm import _safe_json_loads, OpenAILLMClassifier

def test_original_problem():
    """Demonstrate the original problem that would cause JSONDecodeError."""
    
    print("=== Testing JSON Control Character Fix ===\n")
    
    # This is the type of JSON that would cause the original error
    problematic_json = '{"choice": "A", "reasons": ["This has a\nnewline and\ttab"]}'
    
    print("1. Testing problematic JSON with standard json.loads():")
    print(f"   JSON: {problematic_json!r}")
    
    try:
        result = json.loads(problematic_json)
        print(f"   ✓ Standard json.loads() worked: {result}")
    except json.JSONDecodeError as e:
        print(f"   ✗ Standard json.loads() failed: {e}")
    
    print("\n2. Testing same JSON with _safe_json_loads():")
    try:
        result = _safe_json_loads(problematic_json)
        print(f"   ✓ _safe_json_loads() worked: {result}")
        print(f"   ✓ Choice: {result['choice']}")
        print(f"   ✓ Reasons: {result['reasons']}")
    except json.JSONDecodeError as e:
        print(f"   ✗ _safe_json_loads() failed: {e}")

def test_llm_classifier_integration():
    """Test that LLMClassifier can handle responses with control characters."""
    
    print("\n3. Testing LLMClassifier integration:")
    
    # Create a mock classifier
    classifier = OpenAILLMClassifier(
        "test",
        messages=[{"role": "user", "content": "Test"}],
        model="gpt-4",
        choice_scores={"A": 1, "B": 0},
        classification_tools=[],
    )
    
    # Mock response with control characters in the arguments
    mock_response = {
        "tool_calls": [{
            "function": {
                "name": "select_choice",
                "arguments": '{"choice": "A", "reasons": ["This reason has\na newline and\ttab"]}'
            }
        }]
    }
    
    try:
        result = classifier._process_response(mock_response)
        print(f"   ✓ LLMClassifier processed response successfully")
        print(f"   ✓ Score: {result.score}")
        print(f"   ✓ Choice: {result.metadata['choice']}")
        print(f"   ✓ Rationale: {result.metadata['rationale']!r}")
    except Exception as e:
        print(f"   ✗ LLMClassifier failed: {e}")

def test_various_control_characters():
    """Test various control characters that might appear in LLM responses."""
    
    print("\n4. Testing various control characters:")
    
    test_cases = [
        ('{"choice": "A", "reasons": ["Normal text"]}', "Normal text"),
        ('{"choice": "B", "reasons": ["Text with\nnewline"]}', "Newline"),
        ('{"choice": "C", "reasons": ["Text with\ttab"]}', "Tab"),
        ('{"choice": "D", "reasons": ["Text with\rcarriage return"]}', "Carriage return"),
        ('{"choice": "E", "reasons": ["Text with\bbackspace"]}', "Backspace"),
        ('{"choice": "F", "reasons": ["Text with\fform feed"]}', "Form feed"),
        ('{"choice": "G", "reasons": ["Mixed\ncontrol\tchars\rhere"]}', "Multiple control chars"),
    ]
    
    for json_str, description in test_cases:
        try:
            result = _safe_json_loads(json_str)
            print(f"   ✓ {description}: {result['choice']} - {result['reasons'][0]!r}")
        except Exception as e:
            print(f"   ✗ {description}: Failed - {e}")

if __name__ == "__main__":
    test_original_problem()
    test_llm_classifier_integration()
    test_various_control_characters()
    
    print("\n=== Summary ===")
    print("The _safe_json_loads() function successfully handles JSON strings with")
    print("control characters that would normally cause JSONDecodeError in the")
    print("LLMClassifier. This fixes the issue where OpenAI responses containing")
    print("unescaped control characters would crash the evaluation.")
